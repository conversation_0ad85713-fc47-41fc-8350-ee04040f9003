package com.example.stockinventorysystem.util;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.regex.Pattern;

import org.springframework.stereotype.Component;

@Component
public class ValidationUtil {

    private static final Pattern EMAIL_PATTERN = Pattern.compile(
        "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$"
    );

    private static final Pattern USERNAME_PATTERN = Pattern.compile("^[a-zA-Z0-9_]{3,20}$");
    private static final Pattern EMPLOYEE_ID_PATTERN = Pattern.compile("^[A-Z0-9]{3,20}$");
    private static final Pattern PRODUCT_CODE_PATTERN = Pattern.compile("^[A-Z0-9-_]{3,50}$");

    public boolean isValidEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }

    public boolean isValidUsername(String username) {
        return username != null && USERNAME_PATTERN.matcher(username).matches();
    }

    public boolean isValidEmployeeId(String employeeId) {
        return employeeId != null && EMPLOYEE_ID_PATTERN.matcher(employeeId).matches();
    }

    public boolean isValidProductCode(String code) {
        return code != null && PRODUCT_CODE_PATTERN.matcher(code).matches();
    }

    public boolean isValidPassword(String password) {
        return password != null && password.length() >= 6 && password.length() <= 40;
    }

    public boolean isValidPrice(BigDecimal price) {
        return price != null && price.compareTo(BigDecimal.ZERO) > 0;
    }

    public boolean isValidDate(String dateString) {
        if (dateString == null || dateString.trim().isEmpty()) {
            return false;
        }
        
        try {
            LocalDate.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE);
            return true;
        } catch (DateTimeParseException e) {
            return false;
        }
    }

    public boolean isValidQuantity(Integer quantity) {
        return quantity != null && quantity > 0;
    }

    public boolean isNotBlank(String value) {
        return value != null && !value.trim().isEmpty();
    }

    public boolean isValidLength(String value, int maxLength) {
        return value != null && value.length() <= maxLength;
    }

    public String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }
        
        // Remove potentially dangerous characters
        return input.replaceAll("[<>\"'&]", "").trim();
    }

    public String validateAndSanitizeString(String input, int maxLength, String fieldName) {
        if (input == null) {
            throw new IllegalArgumentException(fieldName + " cannot be null");
        }
        
        String sanitized = sanitizeInput(input);
        
        if (sanitized.trim().isEmpty()) {
            throw new IllegalArgumentException(fieldName + " cannot be empty");
        }
        
        if (sanitized.length() > maxLength) {
            throw new IllegalArgumentException(fieldName + " cannot exceed " + maxLength + " characters");
        }
        
        return sanitized;
    }

    public BigDecimal validatePrice(BigDecimal price, String fieldName) {
        if (price == null) {
            throw new IllegalArgumentException(fieldName + " cannot be null");
        }
        
        if (price.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException(fieldName + " must be greater than zero");
        }
        
        if (price.scale() > 2) {
            throw new IllegalArgumentException(fieldName + " cannot have more than 2 decimal places");
        }
        
        return price;
    }

    public LocalDate validateDate(String dateString, String fieldName) {
        if (dateString == null || dateString.trim().isEmpty()) {
            throw new IllegalArgumentException(fieldName + " cannot be null or empty");
        }
        
        try {
            return LocalDate.parse(dateString, DateTimeFormatter.ISO_LOCAL_DATE);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException(fieldName + " must be in format YYYY-MM-DD");
        }
    }

    public Integer validateQuantity(Integer quantity, String fieldName) {
        if (quantity == null) {
            throw new IllegalArgumentException(fieldName + " cannot be null");
        }
        
        if (quantity <= 0) {
            throw new IllegalArgumentException(fieldName + " must be greater than zero");
        }
        
        return quantity;
    }
}
